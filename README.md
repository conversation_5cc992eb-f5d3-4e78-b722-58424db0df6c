# 📦 Microapps Library

Esta es una librería modular que incluye:

- 🧩 **Componentes React reutilizables**
- 🔄 **Hooks personalizados**
- 📚 **Storybook** 
- 🗒️ **Repositorio Plantilla**

## 📚 Uso de Storybook
Puedes explorar todos los componentes y sus variantes en el entorno de Storybook.

```
npm run storybook
```

Los cambios se suben automáticamente en la url cada vez que los subes a la rama main

🔗 [https://green-stone-00f7de803.6.azurestaticapps.net/?path=/docs/como-empezar--docs](https://green-stone-00f7de803.6.azurestaticapps.net/?path=/docs/como-empezar--docs)



## 📦 Librería
Para sacar una nueva versión de la librería modifica el campo version de package.json y ejecuta:

```
npm run build
```

Para añadir la librería a un nuevo repositorio añade la siguiente dependencia en package.json

```
"dependencies": {
    "microapps": "git+https://github.com/Telefonica/microapps-library.git"
    ...    
}
```

Y puedes actualizarla con este comando:

```
npm update microapps
```
