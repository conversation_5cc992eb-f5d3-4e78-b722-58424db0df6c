{"name": "microapps", "version": "1.2.3", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc && npm run build-css && npm run fix-css-imports", "build-css": "sass src:dist --no-source-map", "fix-css-imports": "find dist -type f -name '*.js' -exec sed -i.bak 's/\\.scss/\\.css/g' {} + && find dist -name '*.bak' -delete", "pack": "npm pack", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook-docs": "storybook build --docs", "prepare": "npm run build"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@storybook/addons": "^7.6.17", "react": "^19.1.0", "react-dom": "^19.1.0", "styled-components": "^6.1.18"}, "devDependencies": {"@storybook/addon-a11y": "^9.0.13", "@storybook/addon-docs": "^9.0.13", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-links": "^9.0.13", "@storybook/react-vite": "^9.0.13", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitest/browser": "^3.2.2", "@vitest/coverage-v8": "^3.2.2", "playwright": "^1.52.0", "sass": "^1.89.2", "storybook": "^9.0.13", "storybook-dark-mode": "^4.0.2", "typescript": "^5.8.3", "vitest": "^3.2.2"}, "overrides": {"storybook": "$storybook"}, "engines": {"node": ">=20.0.0"}}