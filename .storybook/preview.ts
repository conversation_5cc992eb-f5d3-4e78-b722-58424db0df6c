// .storybook/preview.ts
import type { Preview } from '@storybook/react';
import { themes } from '@storybook/theming';

const preview: Preview = {
  parameters: {
    docs: {
      theme: themes.dark
    },
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    sidebar: {
      showRoots: true,
    },
    options: {
      storySort: {
        order: ['Como empezar', '*', ['Buttons', 'Items', 'Loading', 'Others', 'Layout']],
      },
    },
    darkMode: {
      current: 'dark',
      dark: themes.dark,
      light: themes.light,
      stylePreview: true,
    },
  },
};

export default preview;
