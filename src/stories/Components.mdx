import { Meta } from "@storybook/addon-docs/blocks";

<Meta title="Componentes y Hooks" />

<div className="container">
	<div className="section-title">
		<h1>Componentes</h1>
		<p>
		Este Storybook contiene dos secciones de componentes: <strong>Generales</strong> y <strong>Específicos</strong>.
		Los componentes Específicos están construidos a partir de los Generales, pero adaptados a casos de uso frecuentes en las Microapps.
		</p>
		<p>
		Primero, revisa si el componente que necesitas está disponible entre los Específicos. Si no es así, consulta la sección de Generales.
		</p>
		<p>
		Debajo de la vista previa de cada componente encontrarás un botón <em>Code</em>, desde el cual puedes desplegar y copiar el código necesario para implementarlo.
		</p>
		<p>
		Asegúrate de utilizar los <strong>parámetros obligatorios</strong>. Solo añade parámetros opcionales como <code>color</code> o <code>radius</code> si son necesarios para adaptar el componente a tu Microapp o añade un <code>className</code> según tu preferencia.
		</p>
	</div>

	<div className="section-title">
		<h1>Hooks</h1>
		<p>
		Además de los componentes, también exponemos una serie de <strong>Hooks personalizados</strong>.Esto es útil porque te permite reutilizar lógica común. Puedes ver los disponibles en la carpeta Hooks.
		</p>
	</div>
</div>

<style>
	{`
		.container {
		margin-bottom: 48px;
		}

		.section-title {
		margin-bottom: 32px;
		}

		.section-title h1 {
		margin-bottom: 16px;
		}

		.section-title p {
		margin-bottom: 12px;
		line-height: 1.6;
		}
	`}
</style>