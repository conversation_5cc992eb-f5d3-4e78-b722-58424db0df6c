import { Meta } from "@storybook/addon-docs/blocks";

import Team from "./assets/team.png";

export const RightArrow = () => (
  <svg
    viewBox="0 0 14 14"
    width="8px"
    height="14px"
    style={{
      marginLeft: '4px',
      display: 'inline-block',
      shapeRendering: 'inherit',
      verticalAlign: 'middle',
      fill: 'currentColor',
    }}
  >
    <path d="m11.1 7.35-5.5 5.5a.5.5 0 0 1-.7-.7L10.04 7 4.9 1.85a.5.5 0 1 1 .7-.7l5.5 5.5c.2.2.2.5 0 .7Z" />
  </svg>
);

<Meta title="Como empezar" />

<div className="container">
  <div className="section-title">
    # Cómo empezar
    <p>
      A pesar de la diversidad en las microapps, todas comparten unos mecanismos comunes. Este Storybook comparte aquellos componentes que obligatoriamente debes usar en tu Microapp, y algunos de uso recurrente que pueden ser de utilidad también.
    </p>
  </div>
</div>

<div className="addon-section">
  <div className="addon-text">
    <h4>Plantilla</h4>
    <p>Comienza un nuevo proyecto utilizando esta plantilla de GitHub donde ya tendrás toda la configuración y componentes iniciales.</p>
    <a href="https://github.com/Telefonica/microapps-template" target="_blank" rel="noreferrer">
      Ir a plantilla en GitHub<RightArrow />
    </a>
  </div>
  <div className="addon-image-wrapper">
    <img src={Team}/>
  </div>
</div>

<style>
  {`
    .container {
  margin-bottom: 48px;
}

.section-title {
  margin-bottom: 32px;
}


.addon-section {
  display: flex;
  align-items: center;
  background-color: #061824;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  height: 280px;
  margin-bottom: 48px;
  position: relative;
  overflow: hidden;
}

.addon-text {
  padding-left: 48px;
  max-width: 240px;
}

.addon-text h4 {
  margin: 0 0 8px 0;
}

.addon-text p {
  margin: 0 0 12px 0;
  color: #FFFFFF;
}

.addon-text a {
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  color: #FFFFFF;
}

.addon-image-wrapper {
  position: absolute;
  left: 345px;
  top: 0;
  height: 100%;
  width: 200%;
  overflow: hidden;
}

.addon-image-wrapper img {
  width: 650px;
  transform: rotate(-15deg);
  margin-left: 40px;
  margin-top: -72px;
  backface-visibility: hidden;
}

@media screen and (max-width: 800px) {
  .addon-image-wrapper {
    left: 300px;
  }
}

@media screen and (max-width: 600px) {
  .addon-section {
    flex-direction: column;
    height: 380px;
    align-items: flex-start;
    padding-top: 32px;
  }

  .addon-text {
    padding-left: 24px;
    max-width: none;
  }

  .addon-image-wrapper {
    position: static;
    width: 124%;
    height: auto;
    margin-top: 16px;
    overflow: visible;
  }

  .addon-image-wrapper img {
    width: 1200px;
    transform: rotate(-12deg);
    margin: 48px 0 -40px -24px;
  }
}

    }
  `}
</style>
