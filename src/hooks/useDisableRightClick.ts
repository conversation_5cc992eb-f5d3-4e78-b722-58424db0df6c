import { useEffect } from "react"

export const useDisableRightClick = () => {
    useEffect(() => {
        const handleRightClick = (event: MouseEvent | TouchEvent) => {
            event.preventDefault()
        }

        document.addEventListener("contextmenu", handleRightClick)

        return () => {
            document.removeEventListener("contextmenu", handleRightClick)
        }
    }, [])
}