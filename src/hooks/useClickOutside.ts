import { useEffect, useRef, RefObject } from "react"

export const useClickOutside = (
  ref: RefObject<HTMLElement>,
  callback: () => void
) => {
    const savedCallback = useRef(callback)

    useEffect(() => {
        savedCallback.current = callback;
    }, [callback])

    useEffect(() => {
        if (!ref?.current) return

        const handleClick = (event: PointerEvent) => {
            const target = event.target as Node
            if (ref.current && !ref.current.contains(target)) {
                savedCallback.current()
            }
        }

        document.addEventListener("pointerdown", handleClick)
        return () => {
        document.removeEventListener("pointerdown", handleClick)
        }
    }, [ref])
};