import { useEffect } from "react"

export const useSpeechStop = (stopSpeech: () => void) => {
    useEffect(() => {
        console.log("useSpeechStop")

        const handleVisibilityChange = () => {
        if (document.hidden) {
            stopSpeech()
        }
        }

        document.addEventListener("visibilitychange", handleVisibilityChange)

        return () => {
        stopSpeech()
        document.removeEventListener("visibilitychange", handleVisibilityChange)
        }
    }, [stopSpeech])
}
