import { useState, useEffect, useRef } from "react"

export const useCopyToClipboard = (resetDelay: number = 2000) => {
    const [copied, setCopied] = useState(false)
    const timeoutRef = useRef<number | null>(null)

    const copy = async (text: string) => {
        if (!navigator?.clipboard?.writeText) {
        console.warn("Clipboard API not supported")
        return;
        }

        try {
        await navigator.clipboard.writeText(text)
        setCopied(true)

        if (timeoutRef.current) clearTimeout(timeoutRef.current)
        timeoutRef.current = window.setTimeout(() => setCopied(false), resetDelay)
        } catch (error) {
        console.error("useCopyToClipboard: error al copiar", error)
        setCopied(false)
        }
    };

    useEffect(() => {
        return () => {
        if (timeoutRef.current) clearTimeout(timeoutRef.current)
        };
    }, []);

  return { copied, copy }
};