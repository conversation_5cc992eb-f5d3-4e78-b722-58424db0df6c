import { useEffect, useState } from "react"

export const useKeyPress = (targetKey: string): boolean => {
    const [pressed, setPressed] = useState(false)

    useEffect(() => {
        if (typeof window === "undefined") return

        const downHandler = (event: KeyboardEvent) => {
        if (event.key === targetKey && !pressed) {
            setPressed(true)
        }
        };

        const upHandler = (event: KeyboardEvent) => {
        if (event.key === targetKey && pressed) {
            setPressed(false)
        }
        };

        window.addEventListener("keydown", downHandler)
        window.addEventListener("keyup", upHandler)

        return () => {
        window.removeEventListener("keydown", downHandler)
        window.removeEventListener("keyup", upHandler)
        };
    }, [targetKey, pressed])

    return pressed
};