// Background
$background: #061824;
$backgroundAlternative: #061824;
$backgroundBrand: #061824;
$backgroundBrandSecondary: #061824;
$backgroundContainer: #081f2e;
$backgroundContainerError: #081f2e;
$backgroundContainerBrand: #081f2e;
$backgroundContainerBrandOverInverse: #081f2e;
$backgroundContainerAlternative: #081f2e;
$backgroundSkeleton: #3c5261;
$backgroundSkeletonInverse: #3c5261;
$backgroundBrandTop: #061824;
$backgroundBrandBottom: #061824;
$appBarBackground: #081f2e;
$navigationBarBackground: #061824;
$skeletonWave: #3c5261;
$backgroundOverlay: #081f2ecc;

// Border
$borderLow: #061824;
$border: #081f2e;
$borderHigh: #6d7d88;
$borderSelected: #0b9cea;

// Buttons
$buttonDangerBackground: #d73241;
$buttonDangerBackgroundPressed: #b22634;
$buttonDangerBackgroundHover: #bf2937;
$buttonPrimaryBackground: #066fcb;
$buttonPrimaryBackgroundInverse: #066fcb;
$buttonPrimaryBackgroundPressed: #055398;
$buttonPrimaryBackgroundHover: #055eac;
$buttonPrimaryBackgroundInversePressed: #055398;
$buttonSecondaryBorder: #ffffff;
$buttonSecondaryBorderPressed: #ffffff;
$buttonSecondaryBorderInverse: #eaebee;
$buttonSecondaryBorderInversePressed: #eaebee;
$textButtonPrimary: #ffffff;
$textButtonPrimaryInverse: #ffffff;
$textButtonPrimaryInversePressed: #ffffff;
$textButtonSecondary: #eaebee;
$textButtonSecondaryPressed: #eaebee;
$textButtonSecondaryInverse: #eaebee;
$textButtonSecondaryInversePressed: #eaebee;
$textLink: #0b9cea;
$textLinkInverse: #0b9cea;
$textLinkDanger: #ff5f6e;
$textLinkSnackbar: #0b9cea;
$textActivated: #0b9cea;
$textBrand: #0b9cea;

// Input
$inputBorder: #89969f;
$inputBorderInverse: #89969f;

// Special
$completedStep: #066fcb;
$completedStepInverse: #066fcb;
$control: #89969f;
$controlActivated: #0b9cea;
$controlInverse: #89969f;
$controlActivatedInverse: #0b9cea;
$controlError: #ff5f6e;
$barTrack: #3c5261;
$barTrackInverse: #3c5261;
$loadingBar: #0b9cea;
$loadingBarBackground: #3c5261;
$toggleAndroidInactive: #eaebee;
$toggleAndroidBackgroundActive: #b3e1fb;
$iosControlKnob: #eaebee;
$controlKnobInverse: #eaebee;
$navigationBarDivider: #061824;
$badge: #d73241;
$feedbackErrorBackground: #d73241;
$feedbackInfoBackground: #0b2739;

// Brand
$brand: #0b9cea;
$brandHigh: #4dbaf7;
$inverse: #eaebee;
$neutralHigh: #eaebee;
$neutralMedium: #89969f;
$neutralMediumInverse: #6b6c6f;
$neutralLow: #032f46;
$neutralLowAlternative: #032f46;

// Text
$textPrimary: #eaebee;
$textPrimaryInverse: #eaebee;
$textSecondary: #89969f;
$textSecondaryInverse: #89969f;

// Adverts
$success: #5cb615;
$warning: #f28d15;
$error: #ff5f6e;
$textError: #ff5f6e;
$textErrorInverse: #ff5f6e;
$promo: #ff5f6e;
$highlight: #eb5f99;
$successLow: #032f46;
$warningLow: #032f46;
$errorLow: #032f46;
$promoLow: #032f46;
$brandLow: #032f46;
$successHigh: #8dcc5b;
$warningHigh: #f6af5b;
$errorHigh: #ff5f6e;
$promoHigh: #ff5f6e;
$successHighInverse: #407f0f;
$warningHighInverse: #6d3f09;
$errorHighInverse: #b22634;
$promoHighInverse: #ff5f6e;

// Bars
$textNavigationBarPrimary: #eaebee;
$textNavigationBarSecondary: #89969f;
$textNavigationSearchBarHint: #89969f;
$textNavigationSearchBarText: #eaebee;
$textAppBar: #89969f;
$textAppBarSelected: #0b9cea;
$customTabsBackground: #061824;

// Tags
$tagTextPromo: #ffffff;
$tagTextActive: #0b9cea;
$tagTextInactive: #ced4d7;
$tagTextInfo: #ffffff;
$tagTextSuccess: #8dcc5b;
$tagTextWarning: #f6af5b;
$tagTextError: #ff5f6e;
$tagBackgroundPromo: #b22634;
$tagBackgroundActive: #032f46;
$tagBackgroundInactive: #032f46;
$tagBackgroundInfo: #032f46;
$tagBackgroundSuccess: #032f46;
$tagBackgroundWarning: #032f46;
$tagBackgroundError: #032f46;
