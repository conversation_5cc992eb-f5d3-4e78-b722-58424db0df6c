@use './colors.scss';

@font-face {
  font-family: OnAir-Light;
  src: url(./assets/fonts/OnAir-Regular.ttf);
}

@font-face {
  font-family: OnAir-Regular;
  src: url(./assets/fonts/OnAir-Regular.ttf);
}

@font-face {
  font-family: OnAir-Bold;
  src: url(./assets/fonts/OnAir-Bold.ttf);
}

* {
  font-family: OnAir-Regular;
  user-select: none;
  list-style: none;
  margin: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  vertical-align: baseline;
  color: colors.$textPrimary;
}

// Heading
.heading1 {
  font-size: 4.5rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 4rem;
  }
}


// Titles
.title1 {
  font-size: 3rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 2rem;
  }
}

.title2 {
  font-size: 1.625rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 1.5rem;
  }
}

.title3 {
  font-size: 1.25rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 1.25rem;
  }
}


// Body
.body1 {
  font-size: 1.375rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 1rem;
  }
}

.body2 {
  font-size: 1.25rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 1rem;
  }
}


// Label
.label1 {
  font-size: 1.25rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 1rem;
  }
}

.label2 {
  font-size: 1.125rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 0.875rem;
  }
}

.label3 {
  font-size: 0.875rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 0.75rem;
  }
}


// Aura
.auraDiscover {
  font-size: 1.5rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 1.25rem;
  }
}

.auraSuggest {
  font-size: 1.375rem;
  line-height: 150%;
  @media (max-width: 1024px) {
    font-size: 1rem;
  }
}

.bold {
  font-family: OnAir-Bold;
}