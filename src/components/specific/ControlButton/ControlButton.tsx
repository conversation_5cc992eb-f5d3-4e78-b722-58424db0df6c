import React, { FC } from 'react';
import ToggleIconButton from '../../general/ToggleIconButton/ToggleIconButton';
import { Icon } from '../../general/Icon/Icons';

interface ControlButtonProps {
	onClick: () => void;
	isActive: boolean;
	type: 'sound' | 'music' | 'media';
	isDisabled?: boolean;
	activeColor?: string;    
	inactiveColor?: string;
	iconColor?: string;
	size: 'small' | 'big';
	className?: string;
	id?:string
}

const getIconsByType = (
	type: 'sound' | 'music' | 'media',
	color?: string
) => {
	switch (type) {
		case 'sound':
			return {
				active: <Icon type='soundOn' color={color} />,
				inactive: <Icon type='soundOff' color={color} />,
			};
		case 'music':
			return {
				active: <Icon type='musicOn' color={color} />,
				inactive: <Icon type='musicOff' color={color} />,
			};
		case 'media':
			return {
				active: <Icon type='pause' color={color} />,
				inactive: <Icon type='play' color={color} />,
			};
		default:
			return {
				active: null,
				inactive: null,
			};
	}
};

const getAriaLabel = (type: string, isActive: boolean) => {
	switch(type) {
		case 'sound':
			return isActive ? 'Silenciar sonido' : 'Activar sonido';
		case 'music':
			return isActive ? 'Pausar música' : 'Reproducir música';
		case 'media':
			return isActive ? 'Pausar medios' : 'Reproducir medios';
		default:
			return '';
	}
}

const ControlButton: FC<ControlButtonProps> = ({
	onClick,
	isActive,
	type,
	isDisabled,
	activeColor,
	inactiveColor,
	iconColor,
	size,
	className,
	id
}) => {
	const { active, inactive } = getIconsByType(type, iconColor);

	const ariaLabel = getAriaLabel(type, isActive);

	return (
		<ToggleIconButton
			onClick={onClick}
			isActive={isActive}
			activeColor={activeColor}
			inactiveColor={inactiveColor}
			activeChild={active}
			inactiveChild={inactive}
			className={`${className ?? ''} control-button`}
			isDisabled={isDisabled}
			size={size}
			ariaLabel={ariaLabel}
			id={id}
		/>
	);
};

export default ControlButton;
