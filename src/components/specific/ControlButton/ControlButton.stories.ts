import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import ControlButton from './ControlButton';

const meta: Meta<typeof ControlButton> = {
  title: 'Specific/Controls/ControlButton',
  component: ControlButton,
  tags: ['autodocs', '!dev'],
  args: {
    onClick: () => console.log('ControlButton clicked'),
    isActive: true,
    isDisabled: false,
  },
  argTypes: {
    type: {
      control: { type: 'select' },
      options: ['sound', 'music', 'media'],
      defaultValue: 'sound'
    },
    size: {
      control: { type: 'radio' },
      options: ['small', 'big'],
      defaultValue: 'big',
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          'Botón de control de multimedia: Sonido, Música o Media.',
      },
    },
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof ControlButton>;

export const Active: Story = {
  name: 'Active',
  args: {
    isActive: true,
    isDisabled: false,
    type: 'sound',
    size: 'big'
  },
};
