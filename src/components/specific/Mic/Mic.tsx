import React, { FC } from 'react';
import './Mic.scss';
import { Icon } from '../../general/Icon/Icons';

interface MicProps {
	onClick: () => void;
	state: 'default' | 'recording' | 'disabled' | 'reset';
	iconColor?: string;
	level: number;
	backgroundColorDefault?: string;
	backgroundColorRecording?: string;
	backgroundColorReset?: string;
	levelColor?: string,
	className?: string;
	id?:string
}

const Mic: FC<MicProps> = ({
	onClick,
	state,
	level,
	backgroundColorDefault,
	backgroundColorRecording,
	backgroundColorReset,
	levelColor,
	iconColor,
	className,
	id
	
}) => {
	const clampedLevel = Math.min(Math.max(level, 0), 100);
	const circle = (clampedLevel / 100) * 50;

	let backgroundColor = backgroundColorDefault;
	if (state === 'recording') {
		backgroundColor = backgroundColorRecording;
	} else if (state === 'reset') {
		backgroundColor = backgroundColorReset;
	}

	const defaultAriaLabel = {
		default: 'Iniciar grabación',
		recording: 'Detener grabación',
		disabled: 'Micrófono deshabilitado',
		reset: 'Reiniciar micrófono'
	}[state];

	return (
		<div className={`${className ?? ''} mic-container`} id={id}>
			{state === 'recording' && (
				<div
					className="level-circle"
					style={{
						backgroundColor: levelColor,
						width: circle * 2 + 64,
						height: circle * 2 + 64
					}}
				/>
			)}
			<button
				type="button"
				className={`mic-button ${state}`}
				onClick={onClick}
				disabled={state === 'disabled'}
				style={{
					backgroundColor,
					color: iconColor
				}}
				aria-label={defaultAriaLabel}
				aria-pressed={state === 'recording' ? true : undefined}
			>
				{state === 'recording' && <Icon type="aura" color={iconColor} />}
				{(state === 'default' || state === 'disabled') && <Icon type="mic" color={iconColor} />}
				{state === 'reset' && <Icon type="reload" color={iconColor} />}
			</button>
		</div>
	);
};

export default Mic;
