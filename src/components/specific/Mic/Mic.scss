$max-radius: 80px;

.mic-container {
	position: relative;
}

.mic-button {
	border: none;
	position: relative;
	padding: 1.5rem;
	border-radius: 50%;
	cursor: pointer;
	align-items: center;
	justify-content: center;
	transition: background-color 0.3s ease;
	z-index: 2;
	background-color: red;
	display: inline-flex;
	&.recording {
		background-color: #032f46;
	}

	&.reset {
		background-color: #055398;
	}

	&.default {
		background-color: #066fcb;
	}

	&:disabled {
		cursor: not-allowed;
		opacity: 0.5;
	}
}

.level-circle {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border-radius: 50%;
	background-color: hsla(201, 91%, 48%, 0.3);
	pointer-events: none;
	z-index: 0;
}