import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import Mic from './Mic'

const meta: Meta<typeof Mic> = {
	title: 'Specific/Controls/Mic',
	component: Mic,
	tags: ['autodocs', '!dev'],
	args: {
		onClick: () => console.log('Mic clicked'),
		state: 'default',
		level: 50
	},
	argTypes: {
		backgroundColorDefault: { control: 'color' },
		iconColor: { control: 'color' },
		backgroundColorRecording: { control: 'color' },
		backgroundColorReset: { control: 'color' },
		state: {
		  control: { type: 'select' },
		  options: ['default', 'recording', 'disabled', 'reset'],
		},
		level: {
			control: { type: 'range', min: 0, max: 100, step: 1 },
			table: {
				disable: false,
			},
		}
	},
	parameters: {
		layout: 'centered',
		docs: {
			description: {
			  component:
				'Botón de micrófono para grabación de audio. Esta enfocado a la interacción con el asistente Aura por lo que los iconos hacen referencia a él.',
			},
		},
		docsOnly: true
	}
}

export default meta
type Story = StoryObj<typeof Mic>

export const Default: Story = {
  	name: 'Default'
}
