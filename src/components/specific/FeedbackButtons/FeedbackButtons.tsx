import React, { FC } from 'react';
import './../../general/IconButton/IconButton.scss';
import { Icon } from '../../general/Icon/Icons';
import ToggleIconButton from '../../general/ToggleIconButton/ToggleIconButton';

interface FeedbackButtonsProps {
  onNegativeFeedback: () => void;
  onPositiveFeedback: () => void;
  feedback: 'positive' | 'negative' | 'none';
  iconColor?: string
  id?:string,
  className?: string
}

const FeedbackButtons: FC<FeedbackButtonsProps> = ({
  onNegativeFeedback,
  onPositiveFeedback,
  feedback,
  iconColor = 'white',
  id,
  className
}) => {
  return (
    <div className={`${className ?? ''}`} style={{ display: 'flex'}} id={id}>
      <ToggleIconButton
        onClick={onNegativeFeedback}
        isActive={feedback === 'negative' ? true : false}
        ariaLabel="Give negative feedback"
        activeChild={<Icon type="thumbDownFilled" color={iconColor} />}
        inactiveChild={<Icon type="thumbDown" color={iconColor} />}
        activeColor='transparent'
        inactiveColor='transparent'
        size='big'
      />

      <ToggleIconButton
        onClick={onPositiveFeedback}
        isActive={feedback === 'positive' ? true : false}
        ariaLabel="Give positive feedback"
        activeChild={<Icon type="thumbUpFilled" color={iconColor} />}
        inactiveChild={<Icon type="thumbUp" color={iconColor} />}
        activeColor='transparent'
        inactiveColor='transparent'
        size='big'
      />
    </div>
  );
};

export default FeedbackButtons;

