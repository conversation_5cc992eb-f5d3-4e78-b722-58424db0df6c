import type { Meta, StoryObj } from '@storybook/react';
import FeedbackButtons from './FeedbackButtons';

const meta: Meta<typeof FeedbackButtons> = {
  title: 'Specific/Data/FeedbackButtons',
  component: FeedbackButtons,
  tags: ['autodocs', '!dev'],
  parameters: {
    docs: {
      description: {
        component:
          'Boón de obtención de feedback, normalmente usado al generar respuestas de imagen o texto con Inteligencia Artificial. Al seleccionar la valoración negativa, debe preguntarse al usuario la razón con el modal correspondiente.',
        },
    },
    layout: 'centered',
    backgrounds: {
        default: 'dark-blue',
        values: [
          { name: 'dark-blue', value: '#061824' },
        ],
    }
  },
};

export default meta;
type Story = StoryObj<typeof FeedbackButtons>;

export const Default: Story = {
  name: 'Positive',
  args: {
    feedback: 'positive'
  },
};
