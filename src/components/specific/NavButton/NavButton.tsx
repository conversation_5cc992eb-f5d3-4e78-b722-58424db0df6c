import React, { FC, useState } from 'react';
import './NavButton.scss';
import { Icon } from '../../general/Icon/Icons';
import IconButton from '../../general/IconButton/IconButton';


interface NavButtonProps {
	onClick: () => void;
	backgroundColor?: string;
	iconColor?: string;
	type: 'menu' | 'back' | 'close' | 'next';
	size: 'small' | 'big';
	id?: string;
	className?: string;
	state?: 'default' | 'disabled';
}

const NavButton: FC<NavButtonProps> = ({
	onClick,
	backgroundColor,
	iconColor,
	type,
	size,
	id,
	className,
	state = 'default'
}) => {
	const ariaLabelMap: Record<NavButtonProps['type'], string> = {
		menu: 'Abrir menú',
		back: 'Atrás',
		close: 'Cerrar',
		next: 'Siguiente'
	};

	return (
		<IconButton
			onClick={onClick}
			state={state}
			size={size}
			backgroundColor={backgroundColor}
			className={`${className ?? ''} button button-nav`}
			ariaLabel={ariaLabelMap[type]}
			id={id}
		>
			<Icon type={type} color={iconColor} />
		</IconButton>
	);
};

export default NavButton;
