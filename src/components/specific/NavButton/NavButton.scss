@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';

.button {
	background-color: colors.$brandLow;
	border-radius: 100%;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 1.25rem;
	border: none;
	cursor: pointer;
	transition: background-color 75ms ease-in-out, transform 75ms ease-in-out;

	&:hover {
		filter: brightness(1.2);
	}

	&.clicked {
		  transform: scale(1.05);
	}

	.icon {
		color: white;
	}

	&.small {
		padding: 0.75rem;
	}
}

