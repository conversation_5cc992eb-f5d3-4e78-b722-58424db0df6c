
import type { Meta, StoryObj } from '@storybook/react'
import NavButton from './NavButton'

const meta: Meta<typeof NavButton> = {
	title: 'Specific/Controls/NavButton',
	component: NavButton,
	tags: ['autodocs', '!dev'],
	args: {
		onClick: () => console.log('NavButton clicked'),
	},
	parameters: {
		docs: {
			description: {
			  component:
				'Botón de navegación general para acciones globales.',
			},
		},
		layout: 'centered',
		docsOnly: true,
		backgrounds: {
			default: 'dark-blue',
			values: [
			  { name: 'dark-blue', value: '#061824' },
			]
		},
	},
}

export default meta
type Story = StoryObj<typeof NavButton>

export const Menu: Story = {
	name: 'Menu',
	args: {
        type: 'menu',
		size: 'big',
    },
}
