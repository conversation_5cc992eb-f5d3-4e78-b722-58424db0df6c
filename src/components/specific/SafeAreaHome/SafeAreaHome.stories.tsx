import type { Meta, StoryObj } from '@storybook/react';
import SafeAreaHome from './SafeAreaHome';

const meta: Meta<typeof SafeAreaHome> = {
	title: 'Specific/SafeArea/SafeAreaHome',
	component: SafeAreaHome,
	tags: ['autodocs', '!dev'],
	parameters: {
		layout: 'centered',
		docs: {
			description: {
				component: 'Respeta este área siempre en el lado derecho superior de la Microapp. Desde la parte Android aparece un componente Botón de forma automática en ese lugar para volver a la Home.',
			},
		},
		backgrounds: {
			default: 'dark-blue',
			values: [
				{ name: 'dark-blue', value: '#061824' },
			],
		},
	},
	argTypes: {
		isVisible: {
			control: 'boolean',
			description: 'Controla si el componente es visible o no.',
			defaultValue: true,
		},
	},
};

export default meta;
type Story = StoryObj<typeof SafeAreaHome>;

export const Default: Story = {
	name: 'Default',
	args: {
		isVisible: true,
	},
};

