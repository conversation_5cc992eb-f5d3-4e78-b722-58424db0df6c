import type { Meta, StoryObj } from '@storybook/react';
import StarsRating from './StarsRating';

const meta: Meta<typeof StarsRating> = {
	title: 'Specific/Data/StarsRating',
	component: StarsRating,
	tags: ['autodocs', '!dev'],
	args: {
		value: 0,
		disabled: false,
		onChange: (value: number) => console.log(`Rating seleccionado: ${value}`),
	},
	argTypes: {
		value: {
		control: {
			type: 'range',
			min: 0,
			max: 5,
			step: 1,
		},
		},
	},
	parameters: {
		layout: 'centered',
		docs: {
		description: {
			component: 'Componente de calificación con estrellas (1 a 5).',
		},
		},
		backgrounds: {
		default: 'dark-blue',
		values: [
			{ name: 'dark-blue', value: '#061824' },
		],
		},
	},
};

export default meta;
type Story = StoryObj<typeof StarsRating>;

export const Default: Story = {
  name: 'Default',
};
