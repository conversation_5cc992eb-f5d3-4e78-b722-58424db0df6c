import React, { FC, useState } from 'react';
import ToggleIconButton from '../ToggleIconButton/ToggleIconButton';
import './StarsRating.scss';
import Icon from '../Icon/Icons';

interface StarsRatingProps {
	value?: number; 
	onChange?: (value: number) => void;
	disabled?: boolean;
	className?: string;
	id?: string;
}

const StarsRating: FC<StarsRatingProps> = ({
 	 value = 0,
	onChange,
	disabled = false,
	className = '',
	id
}) => {
	const [hovered, setHovered] = useState<number | null>(null);

	const currentRating = hovered !== null ? hovered : value;

	const handleClick = (rating: number) => {
			if (!disabled && onChange) {
			onChange(rating);
			}
	};

	return (
		<div
		className={`${className ?? ''} stars-rating`}
		id={id}
		role="radiogroup"
		aria-label="Valoración por estrellas"
		>
		{[1, 2, 3, 4, 5].map((star) => (
			<ToggleIconButton
			key={star}
			onClick={() => handleClick(star)}
			isActive={star <= currentRating}
			isDisabled={disabled}
			activeChild={<Icon type="starFilled"/>}
			inactiveChild={<Icon type="star"/>}
			ariaLabel={`Valorar con ${star} estrella${star > 1 ? 's' : ''}`}
			size="big"
			activeColor='transparent'
			inactiveColor='transparent'
			/>
		))}
		</div>
	);
};

export default StarsRating;
