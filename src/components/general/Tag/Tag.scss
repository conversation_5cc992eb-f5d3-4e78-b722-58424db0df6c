@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';
@use './../../../utils/text.scss';
@use './../../../utils/reset.scss';

.tag {
    display: inline-block;
    padding: 0.25rem 0.95rem;
    border-radius: 5rem; 
    background-color: colors.$tagBackgroundActive; 
    color: colors.$tagTextActive;

    &.active {
        background-color: colors.$tagBackgroundActive;
        color: colors.$tagTextActive;
    }

    &.inactive {
        background-color: colors.$tagBackgroundInactive;
        color: colors.$tagTextInactive;
    }

    &.promo {
        background-color: colors.$tagBackgroundPromo;
        color: colors.$tagTextPromo;
    }

    &.info {
        background-color: colors.$tagBackgroundInfo;
        color: colors.$tagTextInfo;
    }

    &.success {
        background-color: colors.$tagBackgroundSuccess;
        color: colors.$tagTextSuccess;
    }

    &.warning {
        background-color: colors.$tagBackgroundWarning;
        color: colors.$tagTextWarning;
    }

    &.error {
        background-color: colors.$tagBackgroundError;
        color: colors.$tagTextError;
    }
}