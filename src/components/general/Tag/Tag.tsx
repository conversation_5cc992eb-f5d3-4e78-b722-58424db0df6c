import React, { <PERSON> } from 'react';
import './Tag.scss';

interface TagProps {
	text: string;
	type?: 'active' | 'inactive' | 'promo' | 'info' | 'success' | 'warning' | 'error';
	backgroundColor?: string;
	textColor?: string;
	className?: string;
	id?:string
}

const Tag: FC<TagProps> = ({
	text,
	type = 'active',
	backgroundColor,
	textColor,
	className,
	id
}) => {
	return (
		<div
			className={`${className ?? ''} tag ${type}`}
			style={{ backgroundColor }}
			role="note"
			aria-live="polite"
			id={id}
		>
			<span
				className="tag-text label2 bold"
				style={{ color: textColor }}
			>
				{text}
			</span>
		</div>
	);
};

export default Tag;
