import { Meta, StoryObj } from '@storybook/react';
import Tag from './Tag';


const meta: Meta<typeof Tag> = {
	title: 'General/Others/Tag',
	component: Tag,
	tags: ['autodocs', '!dev'],
	args: {
		type: 'active'
	},
	parameters: {
		docs: {
		description: {
			component:
			'Etiqueta para destacar la categoría de un elemento o información relevante.',
		},
		},
			layout: 'centered',
			backgrounds: {
			default: 'dark-blue',
			values: [{ name: 'dark-blue', value: '#061824' }]
		}
	}
}

export default meta

type Story = StoryObj<typeof Tag>

export const Default: Story = {
	args: {
		text: 'Example'
	}
}
