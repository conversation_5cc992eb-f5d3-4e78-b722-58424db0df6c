@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';
@use './../../../utils/text.scss';
@use './../../../utils/reset.scss';

.chip {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 1rem 2.5rem;
	border-radius: 5rem;
	cursor: pointer;
	user-select: none;
	border: 1px solid colors.$control;
	background-color: colors.$backgroundContainer;
	color: colors.$textPrimary;
	transition: all 0.1s ease-in-out;

	@media (max-width: 1024px) {
		padding: 1rem 1.5rem;
	}
	
	&.selected {
		background-color: colors.$brandLow; 
		border: 1px solid colors.$controlActivated;
		color: colors.$textActivated;
	}

	&.clicked {
		transform: scale(0.98);
	}

	&:hover {
		filter: brightness(0.8);
	}
}
