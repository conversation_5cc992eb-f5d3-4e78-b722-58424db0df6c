import { Meta, StoryObj } from '@storybook/react';
import Chip from './Chip'

const meta: Meta<typeof Chip> = {
  title: 'General/Others/Chip',
  component: Chip,
  tags: ['autodocs', '!dev'],
  argTypes: {
    text: { control: 'text' },
    selected: { control: 'boolean' },
    onClick: { action: 'toggled' },
    backgroundColorSelected: { control: 'color' },
    colorSelected: { control: 'color' },
    strokeColorSelected: { control: 'color' }
  },
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'Componente interactivo que permite al usuario seleccionar o deseleccionar opciones.'
      }
    },
  },
  
}

export default meta

type Story = StoryObj<typeof Chip>;

export const Default: Story = {
  args: {
    text: 'Example',
    selected: false,
  }
}