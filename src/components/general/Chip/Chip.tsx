import React, { FC, useState } from 'react';
import './Chip.scss';

interface ChipProps {
  text: string;
  selected: boolean;
  onClick: () => void;
  ariaLabel?: string;
  backgroundColor?: string;
  color?: string;
  strokeColor?: string;
  backgroundColorSelected?: string;
  colorSelected?: string;
  strokeColorSelected?: string;
  borderRadius?: string;
  className?: string;
  id?:string
}

const Chip: FC<ChipProps> = ({
  text,
  selected,
  onClick,
  ariaLabel,
  backgroundColor,
  color,
  strokeColor,
  backgroundColorSelected,
  colorSelected,
  strokeColorSelected,
  borderRadius,
  className,
  id
}) => {
  const [isClicked, setIsClicked] = useState(false);

  const handleClick = () => {
    setIsClicked(true);
    onClick();
    setTimeout(() => setIsClicked(false), 200);
  };

  const classNames = `
    chip
    body2
    ${selected ? 'selected' : 'unselected'}
    ${isClicked ? 'clicked' : ''}
    ${className ?? ''}
  `.trim().replace(/\s+/g, ' ');

  const dynamicStyle: React.CSSProperties = {
    backgroundColor: selected ? backgroundColorSelected ?? undefined : backgroundColor ?? undefined,
    color: selected ? colorSelected ?? undefined : color ?? undefined,
    borderColor: selected ? strokeColorSelected ?? undefined : strokeColor ?? undefined,
    borderRadius
  };

  return (
    <button
      type="button"
      className={classNames}
      style={dynamicStyle}
      onClick={handleClick}
      aria-pressed={selected}
      aria-label={ariaLabel || text}
      id={id}
    >
      {text}
    </button>
  );
};

export default Chip;
