import React, { FC } from 'react';
import './Grid.scss';

interface GridProps {
	elements: React.ReactNode[];
	minItemWidth?: string; 
	width: string;
	id?:string
	className?: string
}

const Grid: FC<GridProps> = ({ 
	elements, 
	minItemWidth = '250px', 
	width,
	id,
	className
}) => {
	const gridStyle: React.CSSProperties = {
		display: 'grid',
		gridTemplateColumns: `repeat(auto-fit, minmax(${minItemWidth}, 1fr))`,
		width: width,
		listStyle: 'none',
		padding: 0,
		margin: 0
	};

	return (
		<ul className={`${className ?? ''} grid-default`} style={gridStyle} id={id}>
		{elements.map((element, index) => (
			<li className='grid-item' key={index}>
			{element}
			</li>
		))}
		</ul>
	);
};

export default Grid;
