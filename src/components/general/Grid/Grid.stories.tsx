import type { <PERSON>a, StoryObj } from '@storybook/react';
import Grid from './Grid';
import React from 'react';
import Image from '../Image/Image';

const meta: Meta<typeof Grid> = {
  title: 'General/Layout/Grid',
  component: Grid,
  tags: ['autodocs', '!dev'],
  args: {
    minItemWidth: '250px',
    width: '1000px',
    elements: [
      <Image width="100%" aspectRatio="1:1" alt="" key="1" />,
      <Image width="100%" aspectRatio="1:1" alt="" key="2" />,
      <Image width="100%" aspectRatio="1:1" alt="" key="3" />,
      <Image width="100%" aspectRatio="1:1" alt="" key="4" />,
      <Image width="100%" aspectRatio="1:1" alt="" key="5" />,
      <Image width="100%" aspectRatio="1:1" alt="" key="6" />,
      <Image width="100%" aspectRatio="1:1" alt="" key="7" />,
    ]
  },
  argTypes: {
    minItemWidth: {
      control: 'text',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '250px' }
      },
    },
    elements: {
      control: false,
      table: {
        type: { summary: 'ReactNode[]' }
      }
    }
  },
  parameters: {
    docs: {
      description: {
        component: 'Componente Grid con ancho mínimo configurable para cada elemento.'
      }
    },
    layout: 'centered'
  }
};

export default meta;
type Story = StoryObj<typeof Grid>;

export const Default: Story = {
  name: 'Default'
};
