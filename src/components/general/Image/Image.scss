@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';

.image-container {
  position: relative;
  overflow: hidden;
  display: block;
  border-radius: radius.$radius-legacyDisplay;
  border: none;
  border-width: 0;

  &.skeleton {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: colors.$backgroundSkeleton;
    border: none;
  }
}

.image {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border: none;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
}

.loading-card {
  animation-delay: inherit;
  background: linear-gradient(90deg, #3C5261 25%, #4A6374 50%, #3C5261 75%);
  background-size: 200% 100%;
  animation: loadingAnimation 1.5s infinite linear;
}

@keyframes loadingAnimation {
  0% {
      background-position: 200% 0;
  }

  100% {
      background-position: -200% 0;
  }
}