import type { Meta, StoryObj } from '@storybook/react'
import Image from './Image'

const meta: Meta<typeof Image> = {
  title: 'General/Items/Image',
  component: Image,
  tags: ['autodocs', '!dev'],
  args: {
    width: '300px',
    aspectRatio: '4:3',
    objectFit: 'cover',
    isLoading: false
  },
  argTypes: {
    aspectRatio: {
      control: { type: 'radio' },
      options: ['1:1', '16:9', '4:3', '7:10']
    },
    objectFit: {
      control: { type: 'select' },
      options: ['cover', 'contain', 'fill', 'scale-down'],
    },
    isLoading: {
      control: { type: 'boolean' }
    }
  },
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark-blue',
      values: [{ name: 'dark-blue', value: '#061824' }],
    },
    docs: {
      description: {
        component:
          'Componente de imagen que permite mostrar imágenes o un placeholder.',
      },
    },
  },
}

export default meta
type Story = StoryObj<typeof Image>

export const WithImage: Story = {
  name: 'Default'
}
