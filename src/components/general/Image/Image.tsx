import React, { FC, useState } from 'react'
import './Image.scss'

interface ImageProps {
  src?: string;
  alt: string;
  width: string;
  aspectRatio: '1:1' | '16:9' | '4:3' | '7:10';
  borderRadius?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down';
  isLoading?: boolean,
  className?: string;
  placeholderSrc?: string;
  id?:string
}

const aspectRatioMap: Record<ImageProps['aspectRatio'], number> = {
  '1:1': 1,
  '16:9': 16 / 9,
  '4:3': 4 / 3,
  '7:10': 7 / 10,
};

const Image: FC<ImageProps> = ({
  src,
  alt,
  width,
  aspectRatio,
  borderRadius,
  objectFit = 'cover',
  isLoading = false,
  className,
  placeholderSrc,
  id
}) => {
  const [hasError, setHasError] = useState(false);

  const handleError = () => setHasError(true);

  const imageToShow = !hasError && src ? src : placeholderSrc;

  return (
    <div
      className={`${className ?? ''} image-container ${!imageToShow ? 'skeleton' : ''} ${isLoading ? 'loading-card' : ''}`}
      style={{
        width,
        aspectRatio: aspectRatioMap[aspectRatio],
        borderRadius,
        position: 'relative',
        overflow: 'hidden',
      }}
      id={id}
    >
      {imageToShow ? (
        <img
          src={imageToShow}
          alt={alt}
          className={`image`}
          style={{
            width: '100%',
            height: '100%',
            objectFit,
            borderRadius,
          }}
          onError={handleError}
        />
      ) : null}
    </div>
  );
};

export default Image;
