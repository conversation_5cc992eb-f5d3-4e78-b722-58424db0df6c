import React, { <PERSON> } from 'react';
import './Bubble.scss';

interface BubbleProps {
	text: string
	backgroundColor?: string
	textColor?: string
	borderRadius?: string
	className?: string
	id?:string
}

const Tag: FC<BubbleProps> = ({ 
	text,
	backgroundColor,
	textColor,
	borderRadius,
	className,
	id
}) => {

	return (
		<span 
			className={`${className ? className : ''} bubble`}
			style={{ backgroundColor: backgroundColor, borderRadius: borderRadius }}
			id={id}
		>
			<p 
				className='bubble-text label2'
				style={{color: textColor}}
			>
				{text}
			</p>
		</span>
	)
}

export default Tag