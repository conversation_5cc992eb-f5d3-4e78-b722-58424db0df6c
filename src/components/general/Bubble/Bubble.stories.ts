import { Meta, StoryObj } from '@storybook/react';
import Bubble from './Bubble';


const meta: Meta<typeof Bubble> = {
	title: 'General/Others/Bubble',
	component: Bubble,
	tags: ['autodocs', '!dev'],
	parameters: {
		docs: {
		description: {
			component:
			'Componente usado para destacar texto en forma de píldora de información.',
		},
		},
			layout: 'centered',
			backgrounds: {
			default: 'dark-blue',
			values: [{ name: 'dark-blue', value: '#061824' }]
		}
	}
}

export default meta

type Story = StoryObj<typeof Bubble>

export const Default: Story = {
	args: {
		text: 'Example'
	}
}
