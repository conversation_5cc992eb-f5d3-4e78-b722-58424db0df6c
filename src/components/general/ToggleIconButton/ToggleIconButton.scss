@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';

.toggle-icon-button {
	border-radius: 50%;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	border: none;
	padding: 1.25rem;
	cursor: pointer;
	transition: background-color 75ms ease-in-out, transform 75ms ease-in-out;
	transform: scale(1);

	&:hover {
		filter: brightness(1.2);
	}

	&.active {
		background-color: colors.$brandLow;
	}

	&.inactive {
		background-color: colors.$buttonDangerBackground;
	}

	&.disabled {
		opacity: 0.5;
		cursor: not-allowed;
		pointer-events: none;
	}

	&.clicked {
		transform: scale(1.05);
	}

	.icon {
		color: white;
		width: 2rem;
		height: 2rem;
	}

	@media (max-width: 1080px) {
		.icon {
			width: 1.5rem;
			height: 1.5rem;
		}
	}

	&.small {
		padding: 0.75rem;
		.icon {
			width: 1.5rem;
			height: 1.5rem;
		}
	}
}
