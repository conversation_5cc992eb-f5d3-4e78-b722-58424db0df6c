import React, { FC, useState, ReactNode } from 'react'
import './ToggleIconButton.scss'
import Icon, { IconType } from '../Icon/Icons'

interface ToggleIconButtonProps {
	onClick: () => void
	isActive: boolean
	isDisabled?: boolean
	activeColor?: string
	inactiveColor?: string
	activeChild: ReactNode
	activeIconType?: IconType
	inactiveChild: ReactNode
	inactiveIconType?: IconType
	className?: string
	ariaLabel: string
	size: 'small' | 'big'
	id?:string
}

const ToggleIconButton: FC<ToggleIconButtonProps> = ({
	onClick,
	isActive,
	isDisabled = false,
	activeColor,
	inactiveColor,
	activeChild,
	activeIconType,
	inactiveChild,
	inactiveIconType,
	className,
	ariaLabel,
	size = 'big',
	id
}) => {
	const [isClicked, setIsClicked] = useState(false);

	const handleClick = () => {
		if (!isDisabled) {
			setIsClicked(true)
			onClick()
			setTimeout(() => setIsClicked(false), 200)
		}
	}

	const backgroundColor = isActive ? activeColor : inactiveColor

	const activeIcon = activeChild ?? (activeIconType ? <Icon type={activeIconType} /> : null)
	const inactiveIcon = inactiveChild ?? (inactiveIconType ? <Icon type={inactiveIconType} /> : null)

	return (
		<button
			type="button"
			onClick={handleClick}
			className={`
				${className ?? ''} 
				toggle-icon-button 
				${isActive ? 'active' : 'inactive'} 
				${isDisabled ? 'disabled' : ''} 
				${isClicked ? 'clicked' : ''}
				${size}
			`}
			style={{ backgroundColor }}
			disabled={isDisabled}
			aria-label={ariaLabel}
			aria-pressed={isActive}
			id={id}
		>
			{isActive ? activeIcon : inactiveIcon}
		</button>
	)
}

export default ToggleIconButton
