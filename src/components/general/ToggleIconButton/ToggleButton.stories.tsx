import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import ToggleIconButton from './ToggleIconButton';
import React from 'react';
import { IconType } from '../Icon/Icons';

const meta: Meta<typeof ToggleIconButton> = {
	title: 'General/Buttons/ToggleIconButton',
	component: ToggleIconButton,
	tags: ['autodocs', '!dev'],
	args: {
		onClick: () => console.log('ToggleIconButton clicked'),
		isActive: false,
		isDisabled: false,
		size: 'big',
		activeIconType: 'addMore',
		inactiveIconType: 'close',
		activeChild: null,
		inactiveChild: null,
	},
	argTypes: {
		activeIconType: {
			control: {
				type: 'select',
				options: [
					'play', 'pause', 'musicOn', 'musicOff',
					'soundOn', 'soundOff', 'aura', 'reload',
					'mic', 'menu', 'thumbUp', 'thumbDown',
					'thumbUpFilled', 'thumbDownFilled', 'close',
					'addMore', 'back'
				] as IconType[]
			},
			table: {
				type: { summary: 'IconType' }
			}
		},
		inactiveIconType: {
			control: {
				type: 'select',
				options: [
					'play', 'pause', 'musicOn', 'musicOff',
					'soundOn', 'soundOff', 'aura', 'reload',
					'mic', 'menu', 'thumbUp', 'thumbDown',
					'thumbUpFilled', 'thumbDownFilled', 'close',
					'addMore', 'back'
				] as IconType[]
			},
			table: {
				type: { summary: 'IconType' }
			}
		},
		activeChild: {
			control: false,
			table: {
				type: { summary: 'ReactNode' }
			},
		},
		inactiveChild: {
			control: false,
			table: {
				type: { summary: 'ReactNode' }
			},
		},
	},
	parameters: {
		docs: {
			description: {
				component: 'Botón de icono con dos estados principales diferenciados: activo e inactivo.'
			}
		},
		layout: 'centered',
		backgrounds: {
			default: 'dark-blue',
			values: [{ name: 'dark-blue', value: '#061824' }]
		}
	}
};

export default meta;
type Story = StoryObj<typeof ToggleIconButton>;

export const Active: Story = {
	name: 'Active',
	args: {
		isActive: true,
	},
};