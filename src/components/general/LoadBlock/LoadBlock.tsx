import React, { FC, useEffect, useState } from 'react'
import './LoadBlock.scss'
import Spinner from '../Spinner/Spinner'

interface LoadBlockProps {
	text?: string | string[],
	interval?: number,
	spinnerColor?: string,
	textColor?: string,
	className?: string,
	id?:string
}

const LoadBlock: FC<LoadBlockProps> = ({
	text,
	interval = 3000,
	spinnerColor,
	textColor,
	className,
	id
}) => {
	const [currentIndex, setCurrentIndex] = useState(0)

	const isArray = Array.isArray(text)
	const textArray = isArray ? text as string[] : [text as string]
	const hasText = textArray.length > 0 && !!textArray[0]

	useEffect(() => {
		if (textArray.length <= 1) return

		const timer = setInterval(() => {
		setCurrentIndex(prev => (prev + 1) % textArray.length)
		}, interval)

		return () => clearInterval(timer)
	}, [textArray, interval])

	return (
		<div 
		className={`${className ?? ''} loadBlock`} 
		role="status" 
		aria-live="polite"
		id={id}
		>
		<span 
			className='text title3 bold'
			style={{color: textColor}}
		>
			{hasText ? textArray[currentIndex] : 'Cargando...'}
		</span>
		<Spinner color={spinnerColor} />
		</div>
	)
}

export default LoadBlock
