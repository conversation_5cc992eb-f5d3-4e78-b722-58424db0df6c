import { Meta, StoryObj } from '@storybook/react';
import LoadBlock from './LoadBlock';

const meta: Meta<typeof LoadBlock> = {
	title: 'General/Loading/LoadBlock',
	component: LoadBlock,
	tags: ['autodocs', '!dev'],
	args: {
		interval: 3000
	},
	parameters: {
		layout: 'centered',
		docsOnly: true,
		backgrounds: {
			default: 'dark-blue',
			values: [
			  { name: 'dark-blue', value: '#061824' },
			],
		},
		docs: {
			description: {
			  component:
				'Componente para informar al usuario mientras se realiza un proceso largo. El texto puede ser cambiante, tan solo hay que proporcionar un array de strings'
			}
		},
	}
};

export default meta;

type Story = StoryObj<typeof LoadBlock>;

export const Default: Story = {
	name: 'Default',
	args: {
		text: 'Loading text example...'
	}
};