@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';
@use './../../../utils/reset.scss';

.icon-button {
	background-color: colors.$brandLow;
	border-radius: 100%;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 1.25rem;
	border: none;
	cursor: pointer;
	transition: background-color 75ms ease-in-out, transform 75ms ease-in-out;

	&.disabled {
		pointer-events: none;
		opacity: 0.6;
	}
	
	.icon {
		color: colors.$textPrimary;
		width: 2rem;
		height: 2rem;
	}

	@media (max-width: 1080px) {
		.icon {
			width: 1.5rem;
			height: 1.5rem;
		}
	}

	&.clicked {
		transform: scale(0.98);
		filter: brightness(0.8);
	}

	&:hover {
		filter: brightness(0.8);
	}

	&.small {
		padding: 0.75rem;
		.icon {
			width: 1.5rem;
			height: 1.5rem;
		}
	}
}

