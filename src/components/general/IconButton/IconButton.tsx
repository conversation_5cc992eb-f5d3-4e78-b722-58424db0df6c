import React, { FC, ReactNode, useState} from 'react'
import './IconButton.scss'
import { Icon, IconType } from '../Icon/Icons'

interface IconButtonProps {
	onClick: () => void
	state: 'default' | 'disabled'
	size: 'small' | 'big'
	backgroundColor?: string
	iconColor?: string,
	className?: string
	ariaLabel: string
	children?: ReactNode
	iconType?: IconType
	id?:string
}

const IconButton: FC<IconButtonProps> = ({
	onClick,
	state,
	backgroundColor,
	iconColor,
	ariaLabel,
	className,
	children,
	iconType,
	size,
	id
}) => {
  	const [isClicked, setIsClicked] = useState(false)

  	const handleClick = () => {
		if (state !== 'disabled') {
			setIsClicked(true)
			onClick()
			setTimeout(() => setIsClicked(false), 150)
		}
  	}

  const disabled = state === 'disabled'
  const content = children ?? (iconType ? <Icon type={iconType} color={iconColor ?? ''} /> : null)

  return (
    <button
		type="button"
		className={`${className ?? ''} icon-button ${isClicked ? 'clicked' : ''} ${disabled ? 'disabled' : ''} ${size}`}
		onClick={handleClick}
		aria-label={ariaLabel}
		style={{ backgroundColor }}
		disabled={disabled}
		id={id}
    >
      	{content}
    </button>
  )
}

export default IconButton
