import React, { FC } from 'react';
import './NackedCard.scss';
import Image from '../Image/Image';
import Tag from '../Tag/Tag';

interface NackedCardProps {
  src?: string;
  alt: string;
  width?: string;
  aspectRatio?: '1:1' | '16:9' | '4:3' | '7:10';
  borderRadius?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down';
  className?: string;
  placeholderSrc?: string;
  ariaLabel: string;
  title?: string;
  pretitle?: string;
  body?: string;
  tags?: string[];
  tagsType?: 'active' | 'inactive' | 'promo' | 'info' | 'success' | 'warning' | 'error';

  topChildren?: React.ReactNode;
  bottomChildren?: React.ReactNode;
  onClick: () => void;
  id?:string
}

const NackedCard: FC<NackedCardProps> = ({
  src,
  alt,
  width = '100%',
  aspectRatio = '7:10',
  borderRadius,
  objectFit,
  className,
  placeholderSrc,
  ariaLabel,
  title,
  pretitle,
  body,
  tags,
  tagsType,
  topChildren,
  bottomChildren,
  onClick,
  id
}) => {
  return (
    <button
      className={`${className ?? ''} card`}
      onClick={onClick}
      aria-label={ariaLabel}
      style={{ width }}
      type="button"
      id={id}
    >
      <Image
        src={src}
        alt={alt}
        width="100%"
        aspectRatio={aspectRatio}
        borderRadius={borderRadius}
        objectFit={objectFit}
        className="card-image"
        placeholderSrc={placeholderSrc}
      />

      <div className="card-content">
        {tags && (
          <div className="card-tags" role="list" aria-label="Etiquetas">
            {tags.map((tag, index) => (
              <Tag key={index} text={tag} type={tagsType} />
            ))}
          </div>
        )}

        {topChildren}
        {pretitle && <span className="card-pretitle body2">{pretitle}</span>}
        {title && <span className="card-title body1 bold">{title}</span>}
        {body && <span className="card-description body2">{body}</span>}
        {bottomChildren}
      </div>
    </button>
  );
};

export default NackedCard;
