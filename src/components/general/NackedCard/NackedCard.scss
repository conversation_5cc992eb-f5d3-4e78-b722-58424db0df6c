@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';
@use './../../../utils/text.scss';
@use './../../../utils/reset.scss';

.card {
    display: flex;
    position: relative;
    width: 100%;
    overflow: hidden;
    cursor: pointer;
    flex-direction: column;
    display: flex;
    gap: 1.3rem;
    @media (max-width: 1080px) {
        gap: 1rem;
    }
}
  
.card-content {
    color: #0B2739;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
    text-align: left;
    width: 100%;
    color: colors.$textPrimary;
    .card-tags {
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;
        gap: 0.625rem;
        width: 100%;
    }
    .card-description {
        color: colors.$textSecondary;
    }
}
  