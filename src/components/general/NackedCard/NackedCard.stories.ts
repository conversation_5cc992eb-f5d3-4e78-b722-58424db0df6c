import type { Meta, StoryObj } from '@storybook/react';
import NackedCard from './NackedCard';

const meta: Meta<typeof NackedCard> = {
    title: 'General/Items/NackedCard',
    component: NackedCard,
    tags: ['autodocs', '!dev'],
    args: {
        width: '300px',
        aspectRatio: '7:10',
        title: 'Title',
        pretitle: 'Pretitle',
        body: 'Body',
        tags: ['label', 'label'],
        ariaLabel: 'Abrir detalle del producto'
    },
    argTypes: {
        aspectRatio: {
            control: { type: 'radio' },
            options: ['1:1', '16:9', '4:3', '7:10'],
        },
        tags: {},
        onClick: { action: 'clicked' },
        bottomChildren: {
            control: false,
            table: {
              type: { summary: 'ReactNode' },
            }
        },
        topChildren: {
            control: false,
            table: {
              type: { summary: 'ReactNode' },
            }
        }
    },
    parameters: {
        docs: {
            description: {
                component: 'Tarjeta de uso general que permite añadir o quitar elementos predefinidos, o añadir customizados con children.',
            },
        },
        layout: 'centered',
        backgrounds: {
            default: 'dark-blue',
            values: [{ name: 'dark-blue', value: '#061824' }],
        },
    },
};

export default meta;
type Story = StoryObj<typeof NackedCard>;

export const WithImage: Story = {
    name: 'Con Imagen'
};
