import React, { FC } from 'react';
import './Banner.scss';
import SecondaryButton from '../SecondaryButton/SecondaryButton';
import IconButton from '../IconButton/IconButton';
import PrimaryButton from '../PrimaryButton/PrimaryButton';
import Icon from '../Icon/Icons';

export interface BannerProps {
	onCancel?: () => void;
	onConfirm?: () => void;
	onClose?: () => void;
	cancelText?: string;
	confirmText?: string;
	title?: string;
	body?: string;
	icon?: React.ReactNode;
	contentChildren?: React.ReactNode;
	id?: string;
	className?: string;
}

const Banner: FC<BannerProps> = ({
	onCancel,
	onConfirm,
	onClose,
	title,
	body,
	icon,
	contentChildren,
	id,
	cancelText = 'Cancelar',
	confirmText = 'Aceptar',
	className
}) => {
	return (
		<div id={id} className={`${className ? className : ''} banner-wrapper ${onClose ? 'top-padding' : ''}`}>

			{/* Close */}
			{onClose && (
				<IconButton
					onClick={onClose}
					size="big"
					state="default"
					iconType="close"
					backgroundColor="transparent"
					ariaLabel="Cerrar banner"
					className="button-close"
				/>
			)}

			{icon}

			{/* Content */}
			<div className="banner-content">
				<div className="banner-header">
					<div className='banner-texts'>
						<h2 className="banner-title title2 bold">{title}</h2>
						{body && <p className="banner-body body1">{body}</p>}
					</div>
				</div>

				{contentChildren}

				{/* Footer / Actions */}
				<div className="banner-footer">
					{onCancel && <SecondaryButton onClick={onCancel} text={cancelText} />}
					{onConfirm && <PrimaryButton onClick={onConfirm} text={confirmText} />}
				</div>
			</div>
		</div>
	);
};

export default Banner;
