@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';
@use './../../../utils/text.scss';

.banner {
	&-wrapper {
		position: relative;
		display: flex;
		flex-direction: row;
		gap: 2rem;
		width: 100%;
		margin: auto;
		background-color: colors.$background;
		box-sizing: border-box;
		padding: 2.5rem 1.5rem 0 1.5rem;
		z-index: 5;

		&.top-padding {
			padding-top: 4rem;
		}

		@media (max-width: 1024px) {
			padding: 2rem 1rem 0 1rem;
			flex-direction: column;
			gap: 1rem;

			&.top-padding {
				padding-top: 3rem;
			}
		}

		.button-close {
		position: absolute;
		top: 0;
		right: 0;
		}
	}

	&-header {
		display: flex;
		flex-direction: row;
		gap: 0.5rem;
		width: 100%;
	}

	&-content {
		flex-grow: 1;
		overflow-y: auto;
		display: flex;
		flex-direction: column;
		gap: 1rem;
		width: 100%;
	}

	&-texts {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
		width: 100%;
	}

	&-footer {
		flex-shrink: 0;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		width: 100%;
		padding: 1.5rem 0;
		gap: 1rem;

		@media (max-width: 1024px) {
			padding: 1rem 0;
		}

		@media (max-width: 600px) {
			flex-direction: column;

			.button-primary,
			.button-secondary {
				width: 100%;
			}
		}
	}
}
