
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import Banner from './<PERSON>';
import type { BannerProps } from './Banner'; 

const meta: Meta<typeof Banner> = {
  title: 'General/Blocks/Banner',
  component: Banner,
  tags: ['autodocs', '!dev'],
  args: {
    title: 'Title',
    body: 'Subtitle',
    cancelText: 'Rechazar',
    confirmText: 'Aceptar'
  },
  argTypes: {
    title: { control: 'text' },
    body: { control: 'text' },
    cancelText: { control: 'text' },
    confirmText: { control: 'text' },
    contentChildren: {
      control: false,
      table: {
        type: { summary: 'ReactNode' },
      },
    },
  },
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'Banner para ser colocado en la parte inferior de la pantalla. No es bloqueante pero anuncia un aviso importante al usuario',
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof Banner>;

export const Default: Story = {
  name: 'Default Modal',
};
