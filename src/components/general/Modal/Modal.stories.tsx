
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import Modal from './Modal';



const meta: Meta<typeof Modal> = {
  title: 'General/Blocks/Modal',
  component: Modal,
  tags: ['autodocs', '!dev'],
  args: {
    title: 'Title',
    body: 'Subtitle',
    cancelText: '<PERSON><PERSON><PERSON>',
    confirmText: 'Aceptar'
  },
  argTypes: {
    title: { control: 'text' },
    body: { control: 'text' },
    cancelText: { control: 'text' },
    confirmText: { control: 'text' },
    contentChildren: {
      control: false,
      table: {
        type: { summary: 'ReactNode' },
      },
    },
  },
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'Modal de pantalla completa que bloquea el uso de la aplicación hasta que el usuario realiza una acción.',
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof Modal>;

export const Default: Story = {
  name: 'Default Modal',
};
