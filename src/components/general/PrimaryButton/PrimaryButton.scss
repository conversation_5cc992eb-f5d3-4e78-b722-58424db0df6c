@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';
@use './../../../utils/text.scss';

.button-primary {
    padding: 1.25rem 2.5rem;
    border: none;
    border-radius: radius.$radius-button; 
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    background-color: colors.$buttonPrimaryBackground;
    color: colors.$textPrimary;
    user-select: none;
    position: relative;
    min-width: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    @media (max-width: 1024px) {
       padding: 1rem;
    }

    &.clicked {
        transform: scale(0.98);
        filter: brightness(0.8);
    }

    &.disabled {
        opacity: 0.6;
        pointer-events: none;
    }

    &.loading {
        pointer-events: none;
    }

    &:hover {
        filter: brightness(0.8);
    }

    .spinner.small {
        border-top: 2px solid colors.$textPrimary;
    }

    .button-content {
        transition: opacity 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        min-width: 100%;
    }

    .button-spinner-wrapper {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        pointer-events: none;
    }
}
