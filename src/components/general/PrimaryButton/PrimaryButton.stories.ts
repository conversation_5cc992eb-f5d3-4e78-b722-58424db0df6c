import type { Meta, StoryObj } from '@storybook/react';
import PrimaryButton from './PrimaryButton';

const meta: Meta<typeof PrimaryButton> = {
  title: 'General/Buttons/PrimaryButton',
  component: PrimaryButton,
  tags: ['autodocs', '!dev'],
  args: {
    onClick: () => console.log('Botón primario clicado.'),
    text: 'Example',
  },
  argTypes: {
    isDisabled: {
      control: { type: 'boolean' }
    },
    isLoading: {
      control: { type: 'boolean' }
    },
  },
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'Botón primario utilizado para acciones principales.',
      },
    },
    backgrounds: {
      default: 'dark-blue',
      values: [
        { name: 'dark-blue', value: '#061824' },
      ],
    },
  },
};

export default meta;
type Story = StoryObj<typeof PrimaryButton>;

export const Active: Story = {
  name: 'Default',
  args: {
    isDisabled: false,
    isLoading: false,
  }
}
