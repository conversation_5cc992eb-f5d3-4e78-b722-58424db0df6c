@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';
@use './../../../utils/text.scss';
@use './../../../utils/reset.scss';

.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
}
  
.spinner {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    border: 5px solid transparent;
    border-top: 5px solid colors.$brand;
    @media (max-width: 1080px){
        width: 3rem;
        height: 3rem
    }

    &.medium {
        width: 3rem;
        height: 3rem;
        border: 4px solid transparent;
        border-top: 4px solid colors.$brand;
        @media (max-width: 1080px){
            width: 2rem;
            height: 2rem
        }
    }

    &.small {
        width: 1.5rem;
        height: 1.5rem;
        border: 2px solid transparent;
        border-top: 2px solid colors.$brand;
        @media (max-width: 1080px){
            width: 1.5rem;
            height: 1.5rem
        }
    }
}

.sr-only {
    position: absolute;
    width: 1px; height: 1px;
    padding: 0; margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    border: 0;
}
  
@keyframes spin {
    to {
      transform: rotate(360deg);
    }
}
  