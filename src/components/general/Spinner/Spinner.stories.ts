import { Meta, StoryObj } from '@storybook/react'
import Spinner from './Spinner'


const meta: Meta<typeof Spinner> = {
  title: 'General/Loading/Spinner',
  component: Spinner,
  tags: ['autodocs', '!dev'],
  args: {
    size: 'large'
  },
  parameters: {
    docs: {
      description: {
        component:
          'Componente para informar al usuario de una operación que está en curso.'
      }
    },
    layout: 'centered',
    backgrounds: {
      default: 'dark-blue',
      values: [{ name: 'dark-blue', value: '#061824' }]
    }
  }
}

export default meta;

type Story = StoryObj<typeof Spinner>

export const Default: Story = {}
