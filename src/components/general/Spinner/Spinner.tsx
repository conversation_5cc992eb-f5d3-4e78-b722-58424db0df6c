import React, { <PERSON> } from 'react'
import './Spinner.scss'

interface SpinnerProps {
	size?: 'small' | 'medium' | 'large'
	color?: string
	className?: string
	id?:string
}

const Spinner: FC<SpinnerProps> = ({
	size = 'large',
	color,
	className,
	id
}) => {

	const borderThickness = size === 'small'
		? 2 : size === 'medium' ? 4 : 5

	return (
		<div 
			role="status"
			aria-live="polite"         
			aria-label="Cargando"
			className={`spinner ${className ?? ''} ${size}`}
			id={id}
			style={color ? { border: `${borderThickness}px solid transparent`, borderTop: `${borderThickness}px solid ${color}` } : {}}
		><span className="sr-only">Cargando</span></div>
	)
}

export default Spinner
