@use './../../../utils/radius.scss';
@use './../../../utils/colors.scss';
@use './../../../utils/text.scss';
@use './../../../utils/reset.scss';

.button-secondary {
    border: none;
    border-radius: radius.$radius-button;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    color: colors.$textButtonSecondary;
    border: 1px solid colors.$buttonSecondaryBorder;
    background-color: transparent;
    user-select: none;
    min-width: 250px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: calc(1.25rem - 1px) calc(2.5rem - 1px);

    @media (max-width: 1024px) {
       padding: 1rem;
    }
  
    &.disabled {
      opacity: 50%;
      cursor: not-allowed;
      opacity: 0.6;
    }
  
    &.clicked {
      transform: scale(0.98);
      filter: brightness(0.8);
    }

    &.loading {
      pointer-events: none;
    }

    &:hover {
      filter: brightness(0.8);
    }

    .spinner.small {
      border-top: 2px solid colors.$textPrimary;
    }

    .button-content {
      transition: opacity 0.2s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .button-spinner-wrapper {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        pointer-events: none;
    }
}