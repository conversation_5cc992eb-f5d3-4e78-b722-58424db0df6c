import React, { FC, useState } from 'react';
import './SecondaryButton.scss';
import Spinner from '../Spinner/Spinner';

interface SecondaryButtonProps {
	onClick: () => void;
	isDisabled?: boolean;
	isLoading?: boolean;
	text: string;
	leftIcon?: React.ReactNode;
	backgroundColor?: string;
	textColor?: string;
	className?: string;
	borderRadius?: string;
	ariaLabel?: string;
	id?:string
}

const SecondaryButton: FC<SecondaryButtonProps> = ({
	onClick,
	isDisabled = false,
	isLoading = false,
	text,
	leftIcon,
	backgroundColor,
	textColor,
	borderRadius,
	className,
	ariaLabel,
	id
}) => {
	const [isClicked, setIsClicked] = useState(false);

	const handleClick = () => {
		if (!isDisabled && !isLoading) {
			setIsClicked(true);
			onClick();
			setTimeout(() => setIsClicked(false), 200);
		}
	};

	return (
		<button
			type="button"
			onClick={handleClick}
			className={`${className ?? ''} button-secondary label1 ${isDisabled ? 'disabled' : ''} ${isLoading ? 'loading' : ''} ${isClicked ? ' clicked' : ''}`}
			disabled={isDisabled}
			style={{ 
				backgroundColor, 
				color: textColor,
				borderRadius: borderRadius 
			}}
			aria-label={ariaLabel}
			id={id}
		>
			<span className="button-content" style={{ opacity: isLoading ? 0 : 1 }}>
				{leftIcon}
				{text}
			</span>
			{isLoading && (
				<div className="button-spinner-wrapper">
					<Spinner color={textColor} size="small" />
				</div>
			)}
		</button>
	);
};

export default SecondaryButton;
