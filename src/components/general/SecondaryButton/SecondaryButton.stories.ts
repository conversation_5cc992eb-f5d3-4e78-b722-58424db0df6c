import type { Meta, StoryObj } from '@storybook/react';
import SecondaryButton from './SecondaryButton';

const meta: Meta<typeof SecondaryButton> = {
  title: 'General/Buttons/SecondaryButton',
  component: SecondaryButton,
  tags: ['autodocs', '!dev'],
  args: {
    onClick: () => console.log('Botón secundario clicado.'),
    text: 'Example',
  },
  argTypes: {
    isDisabled: {
      control: { type: 'boolean' }
    },
    isLoading: {
      control: { type: 'boolean' }
    },
  },
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'Botón para acciones secundarias.',
        }
    },
    backgrounds: {
      default: 'dark-blue',
      values: [
        { name: 'dark-blue', value: '#061824' },
      ],
    },
  },
};

export default meta;
type Story = StoryObj<typeof SecondaryButton>;

export const Active: Story = {
  name: 'Default',
  args: {
    isDisabled: false,
    isLoading: false,
  }
}
